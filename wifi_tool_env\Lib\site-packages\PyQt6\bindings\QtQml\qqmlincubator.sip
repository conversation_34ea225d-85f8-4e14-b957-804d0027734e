// qqmlincubator.sip generated by MetaSIP
//
// This file is part of the QtQml Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QQmlIncubator
{
%TypeHeaderCode
#include <qqmlincubator.h>
%End

public:
    enum IncubationMode
    {
        Asynchronous,
        AsynchronousIfNested,
        Synchronous,
    };

    enum Status
    {
        Null,
        Ready,
        Loading,
        Error,
    };

    QQmlIncubator(QQmlIncubator::IncubationMode mode = QQmlIncubator::Asynchronous);
    virtual ~QQmlIncubator();
    void clear();
    void forceCompletion();
    bool isNull() const;
    bool isReady() const;
    bool isError() const;
    bool isLoading() const;
    QList<QQmlError> errors() const;
    QQmlIncubator::IncubationMode incubationMode() const;
    QQmlIncubator::Status status() const;
    QObject *object() const /Factory/;
    void setInitialProperties(const QVariantMap &initialProperties);

protected:
    virtual void statusChanged(QQmlIncubator::Status);
    virtual void setInitialState(QObject *);

private:
    QQmlIncubator(const QQmlIncubator &);
};

class QQmlIncubationController
{
%TypeHeaderCode
#include <qqmlincubator.h>
%End

public:
    QQmlIncubationController();
    virtual ~QQmlIncubationController();
    QQmlEngine *engine() const;
    int incubatingObjectCount() const;
    void incubateFor(int msecs) /ReleaseGIL/;

protected:
    virtual void incubatingObjectCountChanged(int);

private:
    QQmlIncubationController(const QQmlIncubationController &);
};

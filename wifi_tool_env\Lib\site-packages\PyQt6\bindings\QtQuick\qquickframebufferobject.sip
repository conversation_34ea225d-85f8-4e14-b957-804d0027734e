// qquickframebufferobject.sip generated by MetaSIP
//
// This file is part of the QtQuick Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QOpenGLFramebufferObject /External/;

class QQuickFramebufferObject : public QQuickItem /ExportDerived/
{
%TypeHeaderCode
#include <qquickframebufferobject.h>
%End

public:
    class Renderer /Supertype=PyQt6.sip.wrapper/
    {
%TypeHintCode
try:
    from PyQt6.QtOpenGL import QOpenGLFramebufferObject
except ImportError:
    pass
%End

%TypeHeaderCode
#include <qquickframebufferobject.h>
%End

    protected:
        Renderer();
        virtual ~Renderer();
        virtual void render() = 0;
%If (PyQt_OpenGL)
        virtual QOpenGLFramebufferObject *createFramebufferObject(const QSize &size);
%End
        virtual void synchronize(QQuickFramebufferObject *);
%If (PyQt_OpenGL)
        QOpenGLFramebufferObject *framebufferObject() const;
%End
        void update();
        void invalidateFramebufferObject();
    };

    QQuickFramebufferObject(QQuickItem *parent /TransferThis/ = 0);
    bool textureFollowsItemSize() const;
    void setTextureFollowsItemSize(bool follows);
    virtual QQuickFramebufferObject::Renderer *createRenderer() const = 0 /Factory/;

protected:
    virtual void geometryChange(const QRectF &newGeometry, const QRectF &oldGeometry);
    virtual QSGNode *updatePaintNode(QSGNode *, QQuickItem::UpdatePaintNodeData *);

signals:
    void textureFollowsItemSizeChanged(bool);

public:
    virtual bool isTextureProvider() const;
    virtual QSGTextureProvider *textureProvider() const;
    virtual void releaseResources();
    bool mirrorVertically() const;
    void setMirrorVertically(bool enable);

signals:
    void mirrorVerticallyChanged(bool);
};

// qframe.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QFrame : public QWidget
{
%TypeHeaderCode
#include <qframe.h>
%End

public:
    enum Shadow /BaseType=IntEnum/
    {
        Plain,
        Raised,
        Sunken,
    };

    enum Shape /BaseType=IntEnum/
    {
        NoFrame,
        Box,
        Panel,
        WinPanel,
        HLine,
        VLine,
        StyledPanel,
    };

    enum StyleMask
    {
        Shadow_Mask,
        Shape_Mask,
    };

    QFrame(QWidget *parent /TransferThis/ = 0, Qt::WindowFlags flags = Qt::WindowFlags());
    virtual ~QFrame();
    int frameStyle() const;
    void setFrameStyle(int);
    int frameWidth() const;
    virtual QSize sizeHint() const;
    QFrame::Shape frameShape() const;
    void setFrameShape(QFrame::Shape);
    QFrame::Shadow frameShadow() const;
    void setFrameShadow(QFrame::Shadow);
    int lineWidth() const;
    void setLineWidth(int);
    int midLineWidth() const;
    void setMidLineWidth(int);
    QRect frameRect() const;
    void setFrameRect(const QRect &);

protected:
    virtual bool event(QEvent *e);
    virtual void paintEvent(QPaintEvent *);
    virtual void changeEvent(QEvent *);
    void drawFrame(QPainter *);
    virtual void initStyleOption(QStyleOptionFrame *option) const;
};

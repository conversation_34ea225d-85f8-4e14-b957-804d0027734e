// qgesturerecognizer.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QGestureRecognizer /Supertype=PyQt6.sip.wrapper/
{
%TypeHeaderCode
#include <qgesturerecognizer.h>
%End

public:
    enum ResultFlag /BaseType=Flag/
    {
        Ignore,
        MayBeGesture,
        TriggerGesture,
        FinishGesture,
        CancelGesture,
        ConsumeEventHint,
    };

    typedef QFlags<QGestureRecognizer::ResultFlag> Result;
    QGestureRecognizer();
    virtual ~QGestureRecognizer();
    virtual QGesture *create(QObject *target) /Factory/;
    virtual QGestureRecognizer::Result recognize(QGesture *state, QObject *watched, QEvent *event) = 0;
    virtual void reset(QGesture *state);
    static Qt::GestureType registerRecognizer(QGestureRecognizer *recognizer /Transfer/);
    static void unregisterRecognizer(Qt::GestureType type);
};

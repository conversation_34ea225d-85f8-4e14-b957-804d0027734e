// QtQuickmod.sip generated by MetaSIP
//
// This file is part of the QtQuick Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%Module(name=PyQt6.QtQuick, keyword_arguments="Optional", use_limited_api=True)

%Import QtCore/QtCoremod.sip
%Import QtGui/QtGuimod.sip
%Import QtQml/QtQmlmod.sip

%Copying
Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>

This file is part of PyQt6.

This file may be used under the terms of the GNU General Public License
version 3.0 as published by the Free Software Foundation and appearing in
the file LICENSE included in the packaging of this file.  Please review the
following information to ensure the GNU General Public License version 3.0
requirements will be met: http://www.gnu.org/copyleft/gpl.html.

If you do not wish to use this file under the terms of the GPL version 3.0
then you may purchase a commercial license.  For more information contact
<EMAIL>.

This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
%End

%DefaultSupertype PyQt6.sip.simplewrapper

%Include qquickframebufferobject.sip
%Include qquickgraphicsconfiguration.sip
%Include qquickgraphicsdevice.sip
%Include qquickimageprovider.sip
%Include qquickitem.sip
%Include qquickitemgrabresult.sip
%Include qquickpainteditem.sip
%Include qquickrendercontrol.sip
%Include qquickrendertarget.sip
%Include qquicktextdocument.sip
%Include qquickview.sip
%Include qquickwindow.sip
%Include qsgflatcolormaterial.sip
%Include qsggeometry.sip
%Include qsgimagenode.sip
%Include qsgmaterial.sip
%Include qsgmaterialshader.sip
%Include qsgmaterialtype.sip
%Include qsgnode.sip
%Include qsgrectanglenode.sip
%Include qsgrendererinterface.sip
%Include qsgrendernode.sip
%Include qsgsimplerectnode.sip
%Include qsgsimpletexturenode.sip
%Include qsgtextnode.sip
%Include qsgtexture.sip
%Include qsgtexture_platform.sip
%Include qsgtexturematerial.sip
%Include qsgtextureprovider.sip
%Include qsgvertexcolormaterial.sip

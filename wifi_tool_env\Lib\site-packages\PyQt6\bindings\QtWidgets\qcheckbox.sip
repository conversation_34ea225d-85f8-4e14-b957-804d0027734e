// qcheckbox.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QCheckBox : public QAbstractButton
{
%TypeHeaderCode
#include <qcheckbox.h>
%End

public:
    explicit QCheckBox(QWidget *parent /TransferThis/ = 0);
    QCheckBox(const QString &text, QWidget *parent /TransferThis/ = 0);
    virtual ~QCheckBox();
    virtual QSize sizeHint() const;
    void setTristate(bool on = true);
    bool isTristate() const;
    Qt::CheckState checkState() const;
    void setCheckState(Qt::CheckState state);
    virtual QSize minimumSizeHint() const;

signals:
    void stateChanged(int);
%If (Qt_6_7_0 -)
    void checkStateChanged(Qt::CheckState);
%End

protected:
    virtual bool hitButton(const QPoint &pos) const;
    virtual void checkStateSet();
    virtual void nextCheckState();
    virtual bool event(QEvent *e);
    virtual void paintEvent(QPaintEvent *);
    virtual void mouseMoveEvent(QMouseEvent *);
    virtual void initStyleOption(QStyleOptionButton *option) const;
};

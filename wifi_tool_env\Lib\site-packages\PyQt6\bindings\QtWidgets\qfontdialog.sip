// qfontdialog.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QFontDialog : public QDialog
{
%TypeHeaderCode
#include <qfontdialog.h>
%End

public:
    enum FontDialogOption /BaseType=Flag/
    {
        NoB<PERSON>ons,
        DontUseNativeDialog,
        ScalableFonts,
        NonScalableFonts,
        MonospacedFonts,
        ProportionalFonts,
    };

    typedef QFlags<QFontDialog::FontDialogOption> FontDialogOptions;
    explicit QFontDialog(QWidget *parent /TransferThis/ = 0);
    QFontDialog(const QFont &initial, QWidget *parent /TransferThis/ = 0);
    virtual ~QFontDialog();
    static QFont getFont(bool *ok, const QFont &initial, QWidget *parent = 0, const QString &caption = QString(), QFontDialog::FontDialogOptions options = QFontDialog::FontDialogOptions()) /ReleaseGIL/;
    static QFont getFont(bool *ok, QWidget *parent = 0) /ReleaseGIL/;

protected:
    virtual void changeEvent(QEvent *e);
    virtual void done(int result);
    virtual bool eventFilter(QObject *object, QEvent *event);

public:
    void setCurrentFont(const QFont &font);
    QFont currentFont() const;
    QFont selectedFont() const;
    void setOption(QFontDialog::FontDialogOption option, bool on = true);
    bool testOption(QFontDialog::FontDialogOption option) const;
    void setOptions(QFontDialog::FontDialogOptions options);
    QFontDialog::FontDialogOptions options() const;
    virtual void open();
    void open(SIP_PYOBJECT slot /TypeHint="PYQT_SLOT"/);
%MethodCode
        QObject *receiver;
        QByteArray slot_signature;
        
        if ((sipError = pyqt6_qtwidgets_get_connection_parts(a0, sipCpp, "()", false, &receiver, slot_signature)) == sipErrorNone)
        {
            sipCpp->open(receiver, slot_signature.constData());
        }
        else if (sipError == sipErrorContinue)
        {
            sipError = sipBadCallableArg(0, a0);
        }
%End

    virtual void setVisible(bool visible);

signals:
    void currentFontChanged(const QFont &font);
    void fontSelected(const QFont &font);
};

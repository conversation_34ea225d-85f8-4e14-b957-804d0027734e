@echo off
chcp 65001 >nul
title WIFI校验码计算工具 - ADB风格版启动器

echo.
echo ========================================
echo   🔐 WIFI校验码计算工具 - ADB风格版
echo   By.举个🌰 - 基于Claude Sonnet 4模型开发
echo ========================================
echo.

:: 检查虚拟环境
if exist "wifi_tool_env\Scripts\activate.bat" (
    echo [信息] 发现虚拟环境，正在激活...
    call wifi_tool_env\Scripts\activate.bat
    echo [成功] 虚拟环境已激活
) else (
    echo [警告] 未找到虚拟环境，使用系统Python
)

echo.
echo [启动] 正在启动ADB风格版工具...
echo.

:: 启动ADB风格版
python WIFI_ADB_Style.py

if %errorlevel% neq 0 (
    echo.
    echo [错误] ADB风格版启动失败，尝试启动简洁版...
    python WIFI_Simple.py
    
    if %errorlevel% neq 0 (
        echo.
        echo [错误] 简洁版也启动失败，尝试启动传统版...
        python WIFI.py
        
        if %errorlevel% neq 0 (
            echo.
            echo [错误] 所有版本都启动失败！
            echo [建议] 请检查Python环境和依赖库
            pause
            exit /b 1
        )
    )
)

echo.
echo [完成] 程序已退出
pause

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WIFI校验码计算工具 - ADB风格UI
仿照ADB验证码校验码计算工具的界面设计
By.举个🌰
"""

import sys
from PyQt6.QtWidgets import *
from PyQt6.QtCore import *
from PyQt6.QtGui import *

class WifiADBStyleCalculator(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🔐 WIFI校验码计算工具 - By.举个🌰")
        self.setFixedSize(600, 500)
        self.setWindowIcon(self.create_icon())
        
        # 设置主题
        self.setup_theme()
        self.init_ui()
        
    def create_icon(self):
        pixmap = QPixmap(32, 32)
        pixmap.fill(Qt.GlobalColor.transparent)
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 绘制背景
        gradient = QRadialGradient(16, 16, 16)
        gradient.setColorAt(0, QColor("#4A90E2"))
        gradient.setColorAt(1, QColor("#2E5C8A"))
        painter.setBrush(QBrush(gradient))
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawEllipse(0, 0, 32, 32)
        
        # 绘制WiFi图标
        painter.setPen(QPen(Qt.Global<PERSON>olor.white, 2))
        painter.drawArc(8, 12, 16, 12, 0, 180 * 16)
        painter.drawArc(10, 14, 12, 10, 0, 180 * 16)
        painter.drawArc(12, 16, 8, 8, 0, 180 * 16)
        painter.drawPoint(16, 20)
        
        painter.end()
        return QIcon(pixmap)
        
    def setup_theme(self):
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4A90E2, stop:0.5 #357ABD, stop:1 #2E5C8A);
            }
            QLabel {
                color: white;
                font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
            }
            QFrame {
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 10px;
                background: rgba(255, 255, 255, 0.1);
            }
        """)
        
    def init_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(40, 40, 40, 40)
        main_layout.setSpacing(30)
        
        # 创建主框架
        main_frame = QFrame()
        main_frame.setStyleSheet("""
            QFrame {
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 15px;
                background: rgba(255, 255, 255, 0.1);
                padding: 30px;
            }
        """)
        
        frame_layout = QVBoxLayout(main_frame)
        frame_layout.setSpacing(30)
        frame_layout.setContentsMargins(30, 30, 30, 30)
        
        # 标题
        title_label = QLabel("WIFI校验码计算工具")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 28px;
                font-weight: bold;
                color: white;
                margin: 20px 0;
                border: none;
                background: transparent;
            }
        """)
        frame_layout.addWidget(title_label)
        
        # WiFi状态选择
        status_layout = QHBoxLayout()
        status_layout.setSpacing(30)
        
        status_label = QLabel("WiFi状态:")
        status_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                color: white;
                border: none;
                background: transparent;
            }
        """)
        
        self.hide_radio = QRadioButton("🔒 隐藏WiFi")
        self.show_radio = QRadioButton("👁️ 显示WiFi")
        self.hide_radio.setChecked(True)
        
        radio_style = """
            QRadioButton {
                color: white;
                font-size: 16px;
                font-weight: bold;
                spacing: 8px;
                border: none;
                background: transparent;
            }
            QRadioButton::indicator {
                width: 18px;
                height: 18px;
            }
            QRadioButton::indicator:unchecked {
                border: 2px solid white;
                border-radius: 9px;
                background-color: transparent;
            }
            QRadioButton::indicator:checked {
                border: 2px solid white;
                border-radius: 9px;
                background-color: white;
            }
        """
        self.hide_radio.setStyleSheet(radio_style)
        self.show_radio.setStyleSheet(radio_style)
        
        status_layout.addWidget(status_label)
        status_layout.addWidget(self.hide_radio)
        status_layout.addWidget(self.show_radio)
        status_layout.addStretch()
        
        frame_layout.addLayout(status_layout)
        
        # 验证码输入
        input_label = QLabel("请输入验证码:")
        input_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        input_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: white;
                margin: 10px 0;
                border: none;
                background: transparent;
            }
        """)
        frame_layout.addWidget(input_label)
        
        # 输入框
        self.verify_input = QLineEdit()
        self.verify_input.setPlaceholderText("请输入6位验证码，如：OA6YNY")
        self.verify_input.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.verify_input.setStyleSheet("""
            QLineEdit {
                background-color: white;
                border: 2px solid #ddd;
                border-radius: 8px;
                padding: 15px 20px;
                font-size: 18px;
                font-weight: bold;
                color: #333;
                min-height: 20px;
            }
            QLineEdit:focus {
                border-color: #4A90E2;
                background-color: #f8f9fa;
            }
            QLineEdit::placeholder {
                color: #999;
                font-style: italic;
            }
        """)
        frame_layout.addWidget(self.verify_input)
        
        # 计算按钮
        self.calc_button = QPushButton("计算校验码")
        self.calc_button.setStyleSheet("""
            QPushButton {
                background-color: #5CB85C;
                border: none;
                border-radius: 8px;
                color: white;
                font-size: 18px;
                font-weight: bold;
                padding: 15px 30px;
                min-height: 20px;
            }
            QPushButton:hover {
                background-color: #4CAE4C;
            }
            QPushButton:pressed {
                background-color: #449D44;
            }
        """)
        self.calc_button.clicked.connect(self.calculate_checksum)
        frame_layout.addWidget(self.calc_button)
        
        # 结果标签
        result_label = QLabel("计算结果:")
        result_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: white;
                margin: 20px 0 10px 0;
                border: none;
                background: transparent;
            }
        """)
        frame_layout.addWidget(result_label)
        
        # 结果显示区域
        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)
        self.result_text.setMaximumHeight(120)
        self.result_text.setStyleSheet("""
            QTextEdit {
                background-color: rgba(255, 255, 255, 0.9);
                border: 2px solid rgba(255, 255, 255, 0.5);
                border-radius: 8px;
                padding: 15px;
                font-size: 16px;
                font-weight: bold;
                color: #333;
                font-family: 'Consolas', 'Monaco', monospace;
            }
            QScrollBar:vertical {
                background-color: rgba(255, 255, 255, 0.3);
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: rgba(255, 255, 255, 0.7);
                border-radius: 6px;
                min-height: 20px;
            }
        """)
        frame_layout.addWidget(self.result_text)
        
        # 复制按钮
        self.copy_button = QPushButton("复制校验码")
        self.copy_button.setStyleSheet("""
            QPushButton {
                background-color: #337AB7;
                border: none;
                border-radius: 8px;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 12px 25px;
                min-height: 15px;
            }
            QPushButton:hover {
                background-color: #286090;
            }
            QPushButton:pressed {
                background-color: #204D74;
            }
        """)
        self.copy_button.clicked.connect(self.copy_result)
        frame_layout.addWidget(self.copy_button)
        
        main_layout.addWidget(main_frame)
        
        # 版权信息
        copyright_label = QLabel("© 2025 By.举个🌰 - 基于Claude Sonnet 4模型开发")
        copyright_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        copyright_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.8);
                font-size: 12px;
                margin: 10px 0;
                border: none;
                background: transparent;
            }
        """)
        main_layout.addWidget(copyright_label)
        
        # 连接回车键
        self.verify_input.returnPressed.connect(self.calculate_checksum)
        
    def calculate_checksum(self):
        """计算校验码"""
        verify_code = self.verify_input.text().strip()
        wifi_status = "隐藏" if self.hide_radio.isChecked() else "显示"
        
        if not verify_code:
            QMessageBox.warning(self, "警告", "请输入验证码！")
            return
            
        try:
            # 核心算法：反转 + ASCII转换
            reversed_code = verify_code[::-1]
            ascii_codes = [str(ord(char)) for char in reversed_code]
            checksum = ''.join(ascii_codes)
            
            # 显示结果
            result_text = f"""验证码: {verify_code}
WiFi状态: {wifi_status}
反转后: {reversed_code}
ASCII转换: {' + '.join([f'{char}={ord(char)}' for char in reversed_code])}
校验码: {checksum}"""
            
            self.result_text.setText(result_text)
            
            # 显示成功消息
            QMessageBox.information(self, "计算完成", 
                f"校验码计算完成！\n\n验证码: {verify_code}\n校验码: {checksum}")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"计算失败：{str(e)}")
    
    def copy_result(self):
        """复制校验码到剪贴板"""
        text = self.result_text.toPlainText()
        if "校验码:" in text:
            # 提取校验码
            lines = text.split('\n')
            checksum = ""
            for line in lines:
                if line.startswith("校验码:"):
                    checksum = line.split(":", 1)[1].strip()
                    break
            
            if checksum:
                clipboard = QApplication.clipboard()
                clipboard.setText(checksum)
                QMessageBox.information(self, "复制成功", f"校验码已复制到剪贴板！\n\n{checksum}")
            else:
                QMessageBox.warning(self, "警告", "未找到校验码！")
        else:
            QMessageBox.warning(self, "警告", "没有可复制的校验码！")

def main():
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    # 设置应用信息
    app.setApplicationName("WIFI校验码计算工具")
    app.setApplicationVersion("ADB风格版")
    app.setOrganizationName("By.举个🌰")
    
    window = WifiADBStyleCalculator()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()

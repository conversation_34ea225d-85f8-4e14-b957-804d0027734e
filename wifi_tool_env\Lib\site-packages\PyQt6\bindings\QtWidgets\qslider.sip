// qslider.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QSlider : public QAbstractSlider
{
%TypeHeaderCode
#include <qslider.h>
%End

public:
    enum TickPosition
    {
        NoT<PERSON>,
        <PERSON><PERSON><PERSON><PERSON>ve,
        <PERSON><PERSON><PERSON><PERSON><PERSON>,
        <PERSON><PERSON><PERSON><PERSON><PERSON>,
        T<PERSON>Right,
        TicksBothSides,
    };

    explicit QSlider(QWidget *parent /TransferThis/ = 0);
    QSlider(Qt::Orientation orientation, QWidget *parent /TransferThis/ = 0);
    virtual ~QSlider();
    virtual QSize sizeHint() const;
    virtual QSize minimumSizeHint() const;
    void setTickPosition(QSlider::TickPosition position);
    QSlider::TickPosition tickPosition() const;
    void setTickInterval(int ti);
    int tickInterval() const;
    virtual bool event(QEvent *event);

protected:
    virtual void initStyleOption(QStyleOptionSlider *option) const;
    virtual void paintEvent(QPaintEvent *ev);
    virtual void mousePressEvent(QMouseEvent *ev);
    virtual void mouseReleaseEvent(QMouseEvent *ev);
    virtual void mouseMoveEvent(QMouseEvent *ev);
};

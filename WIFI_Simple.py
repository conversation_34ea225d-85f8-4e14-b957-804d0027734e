#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WIFI校验码计算工具 - 简洁版
只保留核心功能：输入验证码，计算校验码
By.举个🌰
"""

import sys
from PyQt6.QtWidgets import *
from PyQt6.QtCore import *
from PyQt6.QtGui import *

class WifiSimpleCalculator(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🔐 WIFI校验码计算工具 - By.举个🌰")
        self.setFixedSize(500, 400)
        self.setWindowIcon(self.create_icon())
        
        # 设置主题
        self.setup_theme()
        self.init_ui()
        
    def create_icon(self):
        pixmap = QPixmap(32, 32)
        pixmap.fill(Qt.GlobalColor.transparent)
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 绘制背景
        gradient = QRadialGradient(16, 16, 16)
        gradient.setColorAt(0, QColor("#4CAF50"))
        gradient.setColorAt(1, QColor("#2E7D32"))
        painter.setBrush(QBrush(gradient))
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawEllipse(0, 0, 32, 32)
        
        # 绘制WiFi图标
        painter.setPen(QPen(Qt.Global<PERSON>olor.white, 2))
        painter.drawArc(8, 12, 16, 12, 0, 180 * 16)
        painter.drawArc(10, 14, 12, 10, 0, 180 * 16)
        painter.drawArc(12, 16, 8, 8, 0, 180 * 16)
        painter.drawPoint(16, 20)
        
        painter.end()
        return QIcon(pixmap)
        
    def setup_theme(self):
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1a1a1a, stop:0.5 #2d2d2d, stop:1 #1a1a1a);
            }
            QLabel {
                color: #ffffff;
                font-size: 16px;
            }
            QLineEdit {
                background-color: #3d3d3d;
                border: 3px solid #666666;
                border-radius: 10px;
                padding: 15px 20px;
                font-size: 18px;
                font-weight: bold;
                color: #ffffff;
                selection-background-color: #4CAF50;
            }
            QLineEdit:focus {
                border-color: #4CAF50;
                background-color: #4a4a4a;
                box-shadow: 0 0 10px rgba(76, 175, 80, 0.3);
            }
            QLineEdit:hover {
                border-color: #888888;
                background-color: #454545;
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4CAF50, stop:1 #2E7D32);
                border: none;
                border-radius: 12px;
                color: white;
                font-weight: bold;
                font-size: 16px;
                padding: 15px 30px;
                min-width: 120px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #45a049, stop:1 #1B5E20);
            }
            QPushButton:pressed {
                background: #1B5E20;
            }
        """)
        
    def init_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(30)
        layout.setContentsMargins(40, 40, 40, 40)
        
        # 标题
        title_label = QLabel("🔐 WIFI校验码计算工具")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #4CAF50;
                margin: 20px 0;
            }
        """)
        layout.addWidget(title_label)
        
        # WiFi状态选择
        status_layout = QHBoxLayout()
        status_label = QLabel("WiFi状态:")
        status_label.setStyleSheet("font-size: 14px; color: #cccccc;")
        
        self.hide_radio = QRadioButton("🔒 隐藏WiFi")
        self.show_radio = QRadioButton("👁️ 显示WiFi")
        self.hide_radio.setChecked(True)
        
        self.hide_radio.setStyleSheet("""
            QRadioButton {
                color: #ffffff;
                font-size: 14px;
                spacing: 8px;
            }
            QRadioButton::indicator {
                width: 16px;
                height: 16px;
            }
            QRadioButton::indicator:unchecked {
                border: 2px solid #666666;
                border-radius: 8px;
                background-color: #2b2b2b;
            }
            QRadioButton::indicator:checked {
                border: 2px solid #4CAF50;
                border-radius: 8px;
                background-color: #4CAF50;
            }
        """)
        self.show_radio.setStyleSheet(self.hide_radio.styleSheet())
        
        status_layout.addWidget(status_label)
        status_layout.addWidget(self.hide_radio)
        status_layout.addWidget(self.show_radio)
        status_layout.addStretch()
        
        layout.addLayout(status_layout)
        
        # 验证码输入
        verify_label = QLabel("🔤 请输入验证码:")
        verify_label.setStyleSheet("font-size: 18px; font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(verify_label)
        
        self.verify_input = QLineEdit()
        self.verify_input.setPlaceholderText("请输入6位验证码，如：OA6YNY")
        self.verify_input.setStyleSheet("""
            QLineEdit {
                background-color: #3d3d3d;
                border: 3px solid #666666;
                border-radius: 10px;
                padding: 15px 20px;
                font-size: 18px;
                font-weight: bold;
                color: #ffffff;
                selection-background-color: #4CAF50;
            }
            QLineEdit:focus {
                border-color: #4CAF50;
                background-color: #4a4a4a;
                box-shadow: 0 0 10px rgba(76, 175, 80, 0.3);
            }
            QLineEdit:hover {
                border-color: #888888;
                background-color: #454545;
            }
            QLineEdit::placeholder {
                color: #aaaaaa;
                font-style: italic;
            }
        """)
        layout.addWidget(self.verify_input)
        
        # 计算按钮
        self.calc_button = QPushButton("🔄 计算校验码")
        self.calc_button.clicked.connect(self.calculate_checksum)
        layout.addWidget(self.calc_button)
        
        # 校验码输出
        checksum_label = QLabel("🔢 计算结果:")
        checksum_label.setStyleSheet("font-size: 18px; font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(checksum_label)
        
        self.checksum_output = QLineEdit()
        self.checksum_output.setPlaceholderText("校验码将显示在这里")
        self.checksum_output.setReadOnly(True)
        self.checksum_output.setStyleSheet("""
            QLineEdit {
                background-color: #1a4d1a;
                border: 3px solid #4CAF50;
                color: #4CAF50;
                font-weight: bold;
                font-size: 18px;
            }
            QLineEdit:hover {
                background-color: #2a5d2a;
                border-color: #66BB6A;
            }
        """)
        layout.addWidget(self.checksum_output)
        
        # 复制按钮
        self.copy_button = QPushButton("📋 复制校验码")
        self.copy_button.clicked.connect(self.copy_result)
        self.copy_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2196F3, stop:1 #1976D2);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1976D2, stop:1 #1565C0);
            }
            QPushButton:pressed {
                background: #1565C0;
            }
        """)
        layout.addWidget(self.copy_button)
        
        layout.addStretch()
        
        # 版权信息
        copyright_label = QLabel("© 2025 By.举个🌰 - 基于Claude Sonnet 4模型开发")
        copyright_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        copyright_label.setStyleSheet("""
            QLabel {
                color: #888888;
                font-size: 12px;
                margin: 10px 0;
            }
        """)
        layout.addWidget(copyright_label)
        
        # 连接回车键
        self.verify_input.returnPressed.connect(self.calculate_checksum)
        
    def calculate_checksum(self):
        """计算校验码"""
        verify_code = self.verify_input.text().strip()
        wifi_status = "隐藏" if self.hide_radio.isChecked() else "显示"
        
        if not verify_code:
            QMessageBox.warning(self, "警告", "请输入验证码！")
            return
            
        try:
            # 核心算法：反转 + ASCII转换
            reversed_code = verify_code[::-1]
            ascii_codes = [str(ord(char)) for char in reversed_code]
            checksum = ''.join(ascii_codes)
            
            # 显示结果
            self.checksum_output.setText(checksum)
            
            # 显示成功消息
            QMessageBox.information(self, "计算完成", 
                f"WiFi状态: {wifi_status}\n验证码: {verify_code}\n校验码: {checksum}")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"计算失败：{str(e)}")
    
    def copy_result(self):
        """复制结果到剪贴板"""
        checksum = self.checksum_output.text()
        if checksum and checksum != "":
            clipboard = QApplication.clipboard()
            clipboard.setText(checksum)
            QMessageBox.information(self, "复制成功", f"校验码已复制到剪贴板！\n\n{checksum}")
        else:
            QMessageBox.warning(self, "警告", "没有可复制的校验码！")

def main():
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    # 设置应用信息
    app.setApplicationName("WIFI校验码计算工具")
    app.setApplicationVersion("简洁版")
    app.setOrganizationName("By.举个🌰")
    
    window = WifiSimpleCalculator()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()

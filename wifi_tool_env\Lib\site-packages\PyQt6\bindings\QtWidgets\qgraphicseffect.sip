// qgraphicseffect.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QGraphicsEffect : public QObject
{
%TypeHeaderCode
#include <qgraphicseffect.h>
%End

public:
    enum ChangeFlag /BaseType=Flag/
    {
        SourceAttached,
        SourceDetached,
        SourceBoundingRectChanged,
        SourceInvalidated,
    };

    typedef QFlags<QGraphicsEffect::ChangeFlag> ChangeFlags;

    enum PixmapPadMode
    {
        NoPad,
        PadToTransparentBorder,
        PadToEffectiveBoundingRect,
    };

    QGraphicsEffect(QObject *parent /TransferThis/ = 0);
    virtual ~QGraphicsEffect();
    virtual QRectF boundingRectFor(const QRectF &sourceRect) const;
    QRectF boundingRect() const;
    bool isEnabled() const;

public slots:
    void setEnabled(bool enable);
    void update();

signals:
    void enabledChanged(bool enabled);

protected:
    virtual void draw(QPainter *painter) = 0;
    virtual void sourceChanged(QGraphicsEffect::ChangeFlags flags);
    void updateBoundingRect();
    bool sourceIsPixmap() const;
    QRectF sourceBoundingRect(Qt::CoordinateSystem system = Qt::LogicalCoordinates) const;
    void drawSource(QPainter *painter);
    QPixmap sourcePixmap(Qt::CoordinateSystem system = Qt::LogicalCoordinates, QPoint *offset /Out/ = 0, QGraphicsEffect::PixmapPadMode mode = QGraphicsEffect::PadToEffectiveBoundingRect) const;
};

class QGraphicsColorizeEffect : public QGraphicsEffect
{
%TypeHeaderCode
#include <qgraphicseffect.h>
%End

public:
    QGraphicsColorizeEffect(QObject *parent /TransferThis/ = 0);
    virtual ~QGraphicsColorizeEffect();
    QColor color() const;
    qreal strength() const;

public slots:
    void setColor(const QColor &c);
    void setStrength(qreal strength);

signals:
    void colorChanged(const QColor &color);
    void strengthChanged(qreal strength);

protected:
    virtual void draw(QPainter *painter);
};

class QGraphicsBlurEffect : public QGraphicsEffect
{
%TypeHeaderCode
#include <qgraphicseffect.h>
%End

public:
    enum BlurHint /BaseType=Flag/
    {
        PerformanceHint,
        QualityHint,
        AnimationHint,
    };

    typedef QFlags<QGraphicsBlurEffect::BlurHint> BlurHints;
    QGraphicsBlurEffect(QObject *parent /TransferThis/ = 0);
    virtual ~QGraphicsBlurEffect();
    virtual QRectF boundingRectFor(const QRectF &rect) const;
    qreal blurRadius() const;
    QGraphicsBlurEffect::BlurHints blurHints() const;

public slots:
    void setBlurRadius(qreal blurRadius);
    void setBlurHints(QGraphicsBlurEffect::BlurHints hints);

signals:
    void blurRadiusChanged(qreal blurRadius);
    void blurHintsChanged(QGraphicsBlurEffect::BlurHints hints /ScopesStripped=1/);

protected:
    virtual void draw(QPainter *painter);
};

class QGraphicsDropShadowEffect : public QGraphicsEffect
{
%TypeHeaderCode
#include <qgraphicseffect.h>
%End

public:
    QGraphicsDropShadowEffect(QObject *parent /TransferThis/ = 0);
    virtual ~QGraphicsDropShadowEffect();
    virtual QRectF boundingRectFor(const QRectF &rect) const;
    QPointF offset() const;
    qreal xOffset() const;
    qreal yOffset() const;
    qreal blurRadius() const;
    QColor color() const;

public slots:
    void setOffset(const QPointF &ofs);
    void setOffset(qreal dx, qreal dy);
    void setOffset(qreal d);
    void setXOffset(qreal dx);
    void setYOffset(qreal dy);
    void setBlurRadius(qreal blurRadius);
    void setColor(const QColor &color);

signals:
    void offsetChanged(const QPointF &offset);
    void blurRadiusChanged(qreal blurRadius);
    void colorChanged(const QColor &color);

protected:
    virtual void draw(QPainter *painter);
};

class QGraphicsOpacityEffect : public QGraphicsEffect
{
%TypeHeaderCode
#include <qgraphicseffect.h>
%End

public:
    QGraphicsOpacityEffect(QObject *parent /TransferThis/ = 0);
    virtual ~QGraphicsOpacityEffect();
    qreal opacity() const;
    QBrush opacityMask() const;

public slots:
    void setOpacity(qreal opacity);
    void setOpacityMask(const QBrush &mask);

signals:
    void opacityChanged(qreal opacity);
    void opacityMaskChanged(const QBrush &mask);

protected:
    virtual void draw(QPainter *painter);
};
